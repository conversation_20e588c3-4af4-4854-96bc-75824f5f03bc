<?php

namespace App\Http\Controllers;

class IndexController extends Controller
{
    public function index()
    {
        return inertia('home');
    }

    public function contact()
    {
        return inertia('contact', [
            'title' => 'Contact Us',
        ]);
    }

    public function about()
    {
        return inertia('sample-page', [
            'title' => 'About Us',
        ]);
    }

    public function samplePage()
    {
        return inertia('sample-page', [
            'title' => 'Sample Page',
        ]);
    }

    public function travelGuide()
    {
        return inertia('sample-page', [
            'title' => 'Travel Guide',
        ]);
    }

    public function privacy()
    {
        return inertia('sample-page', [
            'title' => 'Privacy Policy',
        ]);
    }

    public function terms()
    {
        return inertia('sample-page', [
            'title' => 'Terms and Conditions',
        ]);
    }

    public function faqs()
    {
        return inertia('faqs', [
            'title' => 'FAQs',
        ]);
    }
}
