<?php

namespace App\Http\Controllers;

use Inertia\Inertia;

class TripController extends Controller
{
    public function destinations()
    {
        return Inertia::render('destinations', [
            'title' => 'Destinations',
        ]);
    }

    public function activities(string $destination)
    {
        return Inertia::render('activities', [
            'title' => 'Nepal',
            'destination' => $destination,
        ]);
    }

    public function activityCategory(string $destination, string $category)
    {
        return Inertia::render('activity-categories', [
            'title' => 'Trekking In Nepal',
            'category' => $category,
            'destination' => $destination,
        ]);
    }

    public function packages(string $destination, string $category)
    {
        return Inertia::render('packages', [
            'title' => 'Everest Treks',
            'category' => $category,
            'destination' => $destination,
        ]);
    }

    public function package(string $destination, string $category, string $slug)
    {
        return Inertia::render('package', [
            'title' => 'Everent Cho La Pass Trek',
            'category' => $category,
            'destination' => $destination,
            'slug' => $slug,
        ]);
    }

    public function customizedTrip()
    {
        return Inertia::render('customized-trip', [
            'title' => 'Customized Trip',
        ]);
    }
}
