<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Tag extends Model
{
    protected $fillable = [
        'name',
        'slug',
    ];

    public function posts(): MorphToMany
    {
        return $this->morphedByMany(Post::class, 'taggable');
    }

    public function packages(): MorphToMany
    {
        return $this->morphedByMany(Package::class, 'taggable');
    }

    public function activities(): MorphToMany
    {
        return $this->morphedByMany(Activity::class, 'taggable');
    }
}
