<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Destination extends Model
{
    protected $fillable = [
        'name',
        'description',
        'image',
    ];

    public function activities(): HasMany
    {
        return $this->hasMany(Activity::class);
    }

    public function packages(): HasMany
    {
        return $this->hasMany(Package::class);
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }
}
