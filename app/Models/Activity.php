<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

class Activity extends Model
{
    protected $fillable = [
        'name',
        'description',
        'image',
        'destination_id',
        'activity_id',
    ];

    public function destination(): BelongsTo
    {
        return $this->belongsTo(Destination::class);
    }

    public function parentActivity(): BelongsTo
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    public function subActivities(): HasMany
    {
        return $this->hasMany(Activity::class, 'activity_id');
    }

    public function packages(): BelongsToMany
    {
        return $this->belongsToMany(Package::class, 'activity_packages');
    }

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tag::class, 'taggable');
    }
}
