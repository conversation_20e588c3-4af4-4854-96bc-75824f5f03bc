### Models
- Destination
  - name
  - description
  - image
  - created_at
  - updated_at

- Activity
  - name
  - description
  - image
  - destination_id // foreign key to Destination
  - activity_id // self-referential foreign key for sub-activities
  - created_at
  - updated_at

- Package
  - name
  - description
  - image
  - destination_id // foreign key to Destination
  - base_price
  - location
  - duration // e.g., in days
  - route_map_url
  - created_at
  - updated_at

- PackageAttribute
  - package_id // foreign key to Package
  - attribute_name
  - attribute_value
  - icon
  - created_at
  - updated_at

- PackagePrice
  - package_id // foreign key to Package
  - price
  - condition // e.g., peak season, group size
  - created_at
  - updated_at

- PackageMedia
  - package_id // foreign key to Package
  - media_type // e.g., image, video
  - media_url
  - created_at
  - updated_at

- PackagePlan
  - package_id // foreign key to Package
  - plan_name
  - day_number
  - description
  - icon
  - created_at
  - updated_at

- PackageCostDetail
  - package_id // foreign key to Package
  - cost_type // e.g., included, excluded
  - description
  - created_at
  - updated_at

- PackageFaq
  - package_id // foreign key to Package
  - question
  - answer
  - created_at
  - updated_at

- PackageReview
    - package_id // foreign key to Package
    - rating // e.g., 1 to 5 stars
    - comment
    - reviewer_name
    - reviewer_email
    - reviewer_phone
    - created_at
    - updated_at

- ActivityPackage
  - activity_id // foreign key to Activity
  - package_id // foreign key to Package

- Post
 - content
   - image
   - type // e.g., page, blog_post, travel_guide, gallery
   - destination_id // foreign key to Destination
   - created_at
   - updated_at - title


- Tag
  - name
  - slug
  - created_at
  - updated_at

- Taggable
  - tag_id // foreign key to Tag
  - taggable_id // polymorphic ID for Post, Package, Activity, etc.
  - taggable_type // polymorphic type (e.g., Post, Package, Activity)
  - created_at
  - updated_at

- ContactMessage
  - first_name
  - last_name
  - email
  - phone
  - subject
  - message
  - created_at
  - updated_at

- CustomizedTrip
  - full_name
  - email
  - phone
  - country_id // foreign key to Country
  - package_id // foreign key to Package
  - travel_date
  - trip_duration // e.g., in days
  - number_of_adults
  - number_of_children
  - estimated_budget
  - notes
  - created_at
  - updated_at

- Country
  - name
  - code
  - dial_code

- Booking
  - package_id // foreign key to Package
  - first_name
  - last_name
  - email
  - phone
  - whatsapp
  - country_id // foreign key to Country
  - booking_date
  - extra_notes // e.g., pickup details and extra requirements
  - status // e.g., confirmed, pending, cancelled
  - created_at
  - updated_at

- BookingPayment
  - booking_id // foreign key to Booking
  - payment_method // e.g., credit_card, bank_transfer, cash
  - amount
  - transaction_id
  - payment_date
  - status // e.g., completed, pending, failed
  - created_at
  - updated_at

- FaqGroup
  - name
  - created_at
  - updated_at

- Faq
  - question
  - answer
  - created_at
  - updated_at

- AskedQuestion
  - name
  - email
  - phone
  - message
  - created_at
  - updated_at

- Setting
  - key // e.g., site_name, contact_email
  - value
  - type // e.g., general, social_media, seo
  - created_at
  - updated_at

- User
  - name
  - email
  - password
  - type // e.g., admin, user
  - created_at
  - updated_at
