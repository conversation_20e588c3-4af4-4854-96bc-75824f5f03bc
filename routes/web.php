<?php

use Illuminate\Support\Facades\Route;

Route::get('/', [App\Http\Controllers\IndexController::class, 'index'])->name('front.home');
Route::get('/contact', [App\Http\Controllers\IndexController::class, 'contact'])->name('front.contact');
Route::get('/customized-trip', [App\Http\Controllers\TripController::class, 'customizedTrip'])->name('front.customized-trip');

Route::get('/destinations', [App\Http\Controllers\TripController::class, 'destinations'])->name('front.destinations');
Route::get('/activities/{destination}', [App\Http\Controllers\TripController::class, 'activities'])->name('front.activities');
Route::get('/activities/{destination}/{category}', [App\Http\Controllers\TripController::class, 'activityCategory'])->name('front.activities.category');
Route::get('/packages/{destination}/{category}', [App\Http\Controllers\TripController::class, 'packages'])->name('front.packages');
Route::get('/packages/{destination}/{category}/{slug}', [App\Http\Controllers\TripController::class, 'package'])->name('front.packages.show');

Route::get('/blogs', [App\Http\Controllers\BlogController::class, 'index'])->name('front.blogs');
Route::get('/blog/{slug}', [App\Http\Controllers\BlogController::class, 'show'])->name('front.blog.show');
Route::get('/about', [App\Http\Controllers\IndexController::class, 'about'])->name('front.about');
Route::get('/travel-guide', [App\Http\Controllers\IndexController::class, 'travelGuide'])->name('front.travel-guide');
Route::get('/company-page', [App\Http\Controllers\IndexController::class, 'samplePage']);
Route::get('/privacy', [App\Http\Controllers\IndexController::class, 'privacy']);
Route::get('/terms', [App\Http\Controllers\IndexController::class, 'terms']);
Route::get('/faqs', [App\Http\Controllers\IndexController::class, 'faqs']);

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', fn() => "")->name('dashboard');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
