// import lodash from 'lodash';
import 'preline';
// import * as VanillaCalendarPro from 'vanilla-calendar-pro';
import '../css/main.css';

import { createInertiaApp } from '@inertiajs/react';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';

// window.VanillaCalendarPro = VanillaCalendarPro;
// window._ = lodash;

const appName = import.meta.env.VITE_APP_NAME || 'Everest Sherpa Adventure';

createInertiaApp({
    title: (title) => {
        if (title === undefined || title === null || title === '') {
            return appName;
        }
        return `${title} - ${appName}`;
    },
    resolve: (name) =>
        resolvePageComponent(
            `./pages/${name}.jsx`,
            import.meta.glob('./pages/**/*.jsx'),
        ),
    setup({ el, App, props }) {
        const root = createRoot(el);

        root.render(
            <StrictMode>
                <App {...props} />
            </StrictMode>,
        );
    },
    progress: {
        color: '#004728',
    },
}).then((res) => res);
