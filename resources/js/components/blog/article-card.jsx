import { Link } from '@inertiajs/react';
import { FiExternalLink } from 'react-icons/fi';
import { twMerge } from 'tailwind-merge';

export default function ArticleCard({
    title,
    imgSrc,
    imgClassName,
    content,
    url,
}) {
    return (
        <div>
            <div className="mb-4">
                <img
                    src={imgSrc}
                    alt="Article"
                    className={twMerge(
                        'aspect-[4/3] w-full rounded-xl object-cover object-center shadow-sm',
                        imgClassName,
                    )}
                />
            </div>
            <Link href={url}>
                <h3 className="ff-myrd-web mb-3 text-xl text-primary md:h-12 md:text-2xl">
                    {title}
                </h3>
            </Link>
            <p className={'md:text-md mt-4 text-sm'}>{content}</p>
            <div className="md:text-md mt-3 flex justify-between text-sm">
                <span className="italic">
                    <strong>Posted on:</strong> March 2024
                </span>
                <Link href={url} className="inline-flex items-center gap-2">
                    Read More
                    <FiExternalLink size={16} />
                </Link>
            </div>
        </div>
    );
}
