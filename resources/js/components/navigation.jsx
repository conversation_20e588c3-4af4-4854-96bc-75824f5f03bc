import MenuItem from './menu-item.jsx';

export default function Navigation({ menuItems }) {
    return (
        <>
            {menuItems.map((item, index) => (
                <MenuItem title={item.title} link={item.href} key={index}>
                    {item.children &&
                        item.children.map((child, childIndex) => (
                            <MenuItem
                                title={child.title}
                                link={child.href}
                                key={childIndex}
                            />
                        ))}
                </MenuItem>
            ))}
        </>
    );
}
