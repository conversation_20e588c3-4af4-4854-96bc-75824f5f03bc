import { Link } from '@inertiajs/react';
import { FaStar } from 'react-icons/fa6';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import { TbCalendarClock } from 'react-icons/tb';

export default function PackageCard({
    imgSrc,
    title,
    destination,
    packageType,
    price,
    oldPrice,
    duration,
    rating,
    ratingCount,
    href,
    forSlider = false
}) {
    return (
        <div
            className={`my-2 w-full ${forSlider ? 'max-w-[300px]' : 'min-w-[275px]'} p-2 group transition duration-300`}
        >
            <div className="w-full rounded-2xl bg-white p-3 shadow-lg border border-gray-50 hover:scale-[101%]">
                <div className="mb-3 w-full rounded-xl overflow-hidden">
                    <img
                        src={imgSrc}
                        alt={title}
                        className="aspect-[3/2] w-full rounded-xl object-cover object-center group-hover:scale-105 transition duration-300"
                    />
                </div>
                <div>
                    <span className="bg-th-green-100 rounded-full px-4 py-1 text-xs font-bold text-primary">
                        {destination} &rarr; {packageType}
                    </span>
                </div>
                <h3
                    className="ff-myrd mt-4 truncate text-xl font-bold text-black"
                    title={title}
                >
                    <Link href={href}>{title}</Link>
                </h3>
                <div className="my-2 flex items-center gap-4">
                    <div className="flex items-center gap-1">
                        <TbCalendarClock className="size-5 text-slate-700" />
                        <span className="text-sm font-semibold text-slate-800">
                            {duration}
                        </span>
                    </div>
                    <div className="flex items-center gap-1">
                        <FaStar className="size-4 text-slate-700" />
                        <span className="text-sm font-semibold text-slate-800">
                            {rating}{' '}
                            <span className="text-slate-500">
                                ({ratingCount})
                            </span>
                        </span>
                    </div>
                </div>
                <div className="mt-3 mb-2 flex items-end justify-between">
                    <div className="mb-0">
                        {oldPrice ? (
                            <div className="text-md">
                                <span className="font-normal text-slate-500 line-through">
                                    ${oldPrice}
                                </span>
                                <span className="font-semibold">/person</span>
                            </div>
                        ) : (
                            <div className="text-md">
                                <span className="font-normal text-transparent line-through">
                                    0
                                </span>
                            </div>
                        )}
                        <span className="font-bold">
                            <span className="text-xl">${price}</span>
                            {!oldPrice && (
                                <span className="!text-md font-semibold">
                                    /person
                                </span>
                            )}
                        </span>
                    </div>
                    <div className="">
                        <Link
                            href={href}
                            className="btn btn-primary rounded-full px-3 py-2 text-sm font-semibold"
                        >
                            Book Now
                            <MdKeyboardDoubleArrowRight className="size-4" />
                        </Link>
                    </div>
                </div>
            </div>
        </div>
    );
}
