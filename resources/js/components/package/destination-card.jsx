import { Link } from '@inertiajs/react';

export default function DestinationCard({ name, image, slug, packages }) {
    return (
        <div className="relative overflow-hidden rounded-xl border border-gray-100 shadow-md">
            <img
                src={image}
                alt={name}
                className="absolute top-0 left-0 -z-1 h-full w-full object-cover object-center"
            />
            <Link href={`/activities/${slug}`}>
                <div className="aspect-[5/3] h-full w-full rounded-xl">
                    <div className="absolute bottom-0 left-0 w-full px-4 pb-1 text-center text-lg font-bold">
                        {name}
                    </div>
                    <div className="absolute top-0 right-0 rounded-bl-xl bg-primary p-3 font-medium text-white shadow-sm">
                        {packages ?? 0} packages
                    </div>
                </div>
            </Link>
            <img
                src="/assets/destinations/abstract-bg.png"
                alt={name}
                className="w-min-[calc(100%+10px)] absolute -bottom-1 left-0 -z-1 w-[700px]! overflow-hidden object-bottom-left"
            />
        </div>
    );
}
