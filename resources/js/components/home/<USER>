import { FaPlay } from 'react-icons/fa6';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';

export default function SolabansVillage() {
    return (
        <section className="relative m-0 w-full overflow-hidden">
            <img
                className="absolute top-0 left-0 -z-1 w-full -translate-y-1 object-cover object-center"
                src="/assets/abstract-watercolor-bg-greenish.png"
                alt=""
            />
            <img
                src={'/assets/solabans-village.png'}
                alt="Solabans Village"
                className="absolute top-0 left-0 -z-2 h-full w-full object-cover"
            />
            <div className="from-th-greenish-500/90 via-th-greenish-500/90 absolute top-0 left-0 -z-1 hidden h-full w-full bg-gradient-to-r from-0% via-47% to-transparent to-55% sm:block"></div>
            <div className="from-th-greenish-500/95 via-th-greenish-500/90 to-th-greenish-500/50 absolute top-0 left-0 -z-1 h-full w-full bg-gradient-to-r from-0% via-70% to-80% sm:hidden"></div>
            <div className="container mx-auto px-4 py-40">
                <div className="sm:w-1/2">
                    <p className="ff-montez mb-4 text-2xl lg:text-3xl">
                        Your Journey contribute to Education in
                    </p>
                    <h2 className="ff-myrd-web mb-4 text-2xl font-bold sm:text-3xl lg:text-5xl">
                        Solabans Village
                    </h2>
                    <p className="ff-myrd w-5/7 font-normal text-slate-800">
                        From its medieval origins to the digital era, learn
                        everything there is to know about the ubiquitous lorem
                        ipsum passage.
                    </p>
                    <div className="mt-6">
                        <a
                            className="btn btn-primary rounded-full"
                            href="javascript:void(0);"
                        >
                            Learn More
                            <MdKeyboardDoubleArrowRight className="size-5" />
                        </a>
                    </div>
                </div>
            </div>
            <img
                className="user-events-none absolute bottom-0 left-0 -z-1 w-full translate-y-2 object-cover object-center"
                src="/assets/abstract-purple-watercolor-background.png"
                alt=""
            />
            <button className="bg-th-greenish-500 absolute top-1/2 -translate-y-1/2 cursor-pointer rounded-full border-12 border-white/75 p-5 text-white hover:bg-primary/75 max-sm:right-4 sm:left-1/2 sm:-translate-x-1/2">
                <FaPlay className="size-8" />
            </button>
        </section>
    );
}
