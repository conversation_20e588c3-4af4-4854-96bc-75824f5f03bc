import { MdKeyboardDoubleArrowRight } from 'react-icons/md';

export default function PlanYourTrip() {
    return (
        <section className="relative pb-20 md:pb-50">
            <div className="bg-graphics absolute inset-0 z-[-1] flex items-center justify-center overflow-hidden">
                <img
                    src="/assets/bg-graphics-1.png"
                    alt="Backaground Graphics"
                    className="absolute bottom-0 left-0 -z-1 w-full object-cover"
                />
                <img
                    src="/assets/bg-graphics-2.png"
                    alt="Backaground Graphics"
                    className="absolute bottom-0 left-0 -z-1 w-full object-cover"
                />
            </div>
            <div className="container px-4 py-8">
                <div className="flex flex-col gap-20 md:flex-row">
                    <div className="aspect-square shrink-0 grow-1 basis-50">
                        <div className="grid grid-cols-2 gap-4">
                            <div className="col-span-1">
                                <img
                                    src="/assets/plan-trip-1.png"
                                    alt="Plan Your Trip"
                                    className="photo-card-shadow h-full w-full rounded-full !rounded-br-none object-cover"
                                />
                            </div>
                            <div className="flex flex-col gap-4">
                                <img
                                    src="/assets/plan-trip-2.png"
                                    alt="Plan Your Trip"
                                    className="aspect-square h-full w-full rounded-full !rounded-bl-none object-cover"
                                />
                                <img
                                    src="/assets/plan-trip-3.png"
                                    alt="Plan Your Trip"
                                    className="aspect-square h-full w-full rounded-full !rounded-tr-none object-cover"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="py-4 md:max-w-1/2">
                        <div className="md:w-3/4">
                            <p className="ff-montez mb-2 text-2xl text-primary md:text-3xl">
                                Let's Go Together
                            </p>
                            <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                                Plan Your Trip{' '}
                                <br className={'hidden md:block'} /> With Us
                            </h2>
                            <p className="ff-myrd font-semibold text-slate-800">
                                From its medieval origins to the digital era,
                                learn everything there is to know about the
                                ubiquitous lorem ipsum passage.
                            </p>
                        </div>
                        <div className="my-4">
                            <div className="mb-4 flex gap-6">
                                <div className="flex size-16 shrink-0 grow-1 items-center justify-center rounded-full bg-primary text-white">
                                    <img
                                        src="/assets/world-tour-icon.png"
                                        alt="Icon"
                                        className="size-10 brightness-0 invert"
                                    />
                                </div>
                                <div>
                                    <h3 className="ff-myrd-web text-2xl text-primary">
                                        Exclusive Trip
                                    </h3>
                                    <p className="ff-myrd-web text-slate-800">
                                        From its medieval origins to the digital
                                        era, learn there is to know about the
                                        ubiquitous.
                                    </p>
                                </div>
                            </div>
                            <div className="mb-4 flex gap-6">
                                <div className="flex size-16 shrink-0 grow-1 items-center justify-center rounded-full bg-primary text-white">
                                    <img
                                        src="/assets/tour-guide-icon.png"
                                        alt="Icon"
                                        className="size-10 brightness-0 invert"
                                    />
                                </div>
                                <div>
                                    <h3 className="ff-myrd-web text-2xl text-primary">
                                        Professional Guide
                                    </h3>
                                    <p className="ff-myrd-web text-slate-800">
                                        From its medieval origins to the digital
                                        era, learn there is to know about the
                                        ubiquitous.
                                    </p>
                                </div>
                            </div>
                            <a
                                className="btn btn-primary rounded-full"
                                href="javascript:void(0);"
                            >
                                Learn More
                                <MdKeyboardDoubleArrowRight className="size-5" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
