import { SplideSlide } from '@splidejs/react-splide';
import PackageCard from '../package/package-card.jsx';
import DragButton from '../ui/drag-button.jsx';
import DragSlider from '../ui/drag-slider.jsx';
import { packages } from '../../data/dummy';

export default function BestOffers() {

    return (
        <section className="relative pb-6">
            <div className="absolute top-0 left-0 -z-1 h-1/2 min-h-80 w-full overflow-hidden bg-[#0A5333]">
                <img
                    className="absolute bottom-0 left-0 -z-1 w-full min-w-[1600px] translate-y-1/5 object-cover object-center"
                    alt=""
                    src="/assets/abstract-purple-watercolor-background.png"
                ></img>
            </div>
            <div className="container px-4 py-2">
                <div className="py-4 text-center">
                    <p className="ff-montez mb-2 text-2xl text-white md:text-3xl">
                        Unique and Exclusive!
                    </p>
                    <h2 className="ff-myrd-web mb-3 text-4xl text-white md:text-5xl">
                        Discover our Best Offers!
                    </h2>
                </div>
                <div className="relative">
                    <div className="flex gap-6">
                        <DragSlider label="Best Offers">
                            {packages.length > 0 &&
                                packages.map((_package, index) => (
                                    <SplideSlide key={index}>
                                        <PackageCard
                                            key={index}
                                            title={_package.title}
                                            price={_package.price}
                                            oldPrice={_package.oldPrice}
                                            duration={_package.duration}
                                            rating={_package.rating}
                                            ratingCount={_package.ratingCount}
                                            imgSrc={_package.imgSrc}
                                            destination={_package.destination}
                                            packageType={_package.packageType}
                                            forSlider={true}
                                        />
                                    </SplideSlide>
                                ))}
                        </DragSlider>
                    </div>
                    <DragButton />
                </div>
            </div>
        </section>
    );
}
