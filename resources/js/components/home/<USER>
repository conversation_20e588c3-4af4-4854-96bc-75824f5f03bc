import { useState } from 'react';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import TopDestinationCard from './top-destination-card.jsx';

export default function TopDestinations() {
    const [destSelected, setDestSelected] = useState('nepal');
    const activeClass = 'bg-th-green-800 text-white';
    const inactiveClass = 'bg-th-green-100 text-slate-800';

    const destinations = [
        { slug: 'nepal', name: 'Nepal' },
        { slug: 'india', name: 'India' },
        { slug: 'tibet', name: 'Tibet' },
        { slug: 'bhutan', name: 'Bhutan' }
    ];

    return (
        <section>
            <div className="container px-4 py-8">
                <div className="py-4 text-center">
                    <p className="ff-montez mb-2 text-2xl text-primary md:text-3xl">
                        Most Popular Travel Spot
                    </p>
                    <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                        Top Destinations
                    </h2>
                    <p className="ff-myrd mx-auto text-sm font-semibold text-slate-800 md:w-4/7 md:text-lg">
                        From its medieval origins to the digital era, learn
                        everything there is to know about the ubiquitous lorem
                        ipsum passage.
                    </p>
                </div>
                <div className="mt-4 mb-8">
                    <div className="flex gap-4 overflow-y-auto pb-2 md:justify-center">
                        {destinations.map((destination) => (
                            <button
                                key={destination.slug}
                                className={`${destSelected === destination.slug ? activeClass : inactiveClass} text-th-green-800 hover:bg-th-green-800 cursor-pointer rounded-full px-10 py-2 font-semibold transition-colors duration-300 hover:text-white`}
                                onClick={() =>
                                    setDestSelected(destination.slug)
                                }
                            >
                                {destination.name}
                            </button>
                        ))}
                    </div>
                </div>
                <div className="grid-cols-7 gap-6 max-lg:flex max-lg:flex-col lg:grid">
                    <div className="col-span-2 gap-6 max-lg:flex max-sm:flex-col lg:grid">
                        <TopDestinationCard
                            title="Kailash Overland Tour"
                            bgImgSrc="/assets/trek-6.png"
                        />
                        <TopDestinationCard
                            title="Panch Pokhari Trek"
                            bgImgSrc="/assets/panch-pokhari-trek.png"
                        />
                    </div>
                    <div className="col-span-2">
                        <TopDestinationCard
                            title="Lang Tang Valley Trek"
                            bgImgSrc="/assets/trek-4.png"
                            className={'h-full'}
                        />
                    </div>
                    <div className="col-span-3">
                        <div className="grid-cols-5 gap-6 max-sm:flex max-sm:flex-col sm:grid">
                            <div className="col-span-5">
                                <TopDestinationCard
                                    title="Kailash Helicopter Tour"
                                    bgImgSrc="/assets/trek-5.png"
                                />
                            </div>
                            <div className="col-span-2">
                                <TopDestinationCard
                                    title="Jomsom to Mukhtinath"
                                    bgImgSrc="/assets/trek-3.png"
                                    imgClassName="object-right-bottom"
                                />
                            </div>
                            <div className="col-span-3">
                                <TopDestinationCard
                                    title="Trishuli Rafting Day Tour"
                                    bgImgSrc="/assets/trek-1.png"
                                />
                            </div>
                        </div>
                    </div>
                </div>
                <div className="mt-8 flex justify-center">
                    <a className="inline-flex cursor-pointer gap-1 rounded-full border border-[#004728] px-6 py-2 text-sm font-semibold text-[#004728] transition-colors duration-300 hover:bg-[#004728] hover:text-white">
                        View More
                        <MdKeyboardDoubleArrowRight className="size-5" />
                    </a>
                </div>
            </div>
        </section>
    );
}
