import { twMerge } from 'tailwind-merge';

export default function TopDestinationCard({
    title,
    bgImgSrc,
    className,
    imgClassName,
}) {
    return (
        <>
            <div
                className={twMerge(
                    'photo-card-shadow relative h-60 w-full overflow-hidden rounded-lg bg-gray-200',
                    className,
                )}
            >
                <img
                    src={bgImgSrc}
                    alt="Top Destination 1"
                    className={twMerge(
                        'text-md h-full w-full rounded-lg object-cover object-center',
                        imgClassName,
                    )}
                />
                <img
                    src="/assets/abstract-text-bg-1.png"
                    alt="Background"
                    className="absolute -bottom-10 -left-[50px] w-[calc(100%+100px)]! min-w-[1000px]"
                />
                <div className="absolute bottom-2 left-0 w-full text-center">
                    <span className="font-bold">{title}</span>
                </div>
            </div>
        </>
    );
}
