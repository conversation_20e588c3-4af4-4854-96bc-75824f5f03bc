import { SplideSlide } from '@splidejs/react-splide';
import DragSlider from '../ui/drag-slider.jsx';
import TestimonialCard from './testimonial-card.jsx';

export default function Testimonials() {
    return (
        <section className="w-full overflow-hidden">
            <div className="container px-4 py-8">
                <div className="py-4 text-center">
                    <p className="ff-montez mb-2 text-2xl text-primary md:text-3xl">
                        Testimonials
                    </p>
                    <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                        What Client Say?
                    </h2>
                </div>
            </div>
            <div className="mt-4 mb-8">
                <div className="flex justify-center gap-4">
                    <DragSlider perPage={3.7} label={'Travel Experiences'}>
                        <SplideSlide>
                            <TestimonialCard isActive={true} />
                        </SplideSlide>
                        <SplideSlide>
                            <TestimonialCard isActive={true} />
                        </SplideSlide>
                        <SplideSlide>
                            <TestimonialCard isActive={true} />
                        </SplideSlide>
                    </DragSlider>
                </div>
            </div>
        </section>
    );
}
