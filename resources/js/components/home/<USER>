import { FaCirclePlay } from 'react-icons/fa6';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import HeroPackageSlider from './slider/hero-package-slider.jsx';

export default function HeroSection({ children }) {
    const bgImg = '/assets/hero-bg.png';
    const bgAbstract = '/assets/abstract-purple-watercolor-background.png';

    return (
        <section className="relative mt-8 h-auto w-full overflow-hidden md:h-[80vh] landing-hero-section"
                 style={{
                     '--bg-img': `url(${bgImg})`,
                     '--bg-abstract': `url(${bgAbstract})`
                 }}
        >
            <div className="top-0 left-0 m-0 h-full w-full p-0 text-white max-md:mb-[70px] md:absolute md:min-h-[80vh]">
                <div className="container max-md:mt-24! flex h-full items-center px-4 md:mt-0 md:px-6">
                    <div className="md:w-1/2 md:pr-[75px]">
                        <p className="font-montez hero-pretitle mb-6 text-3xl md:text-2xl lg:text-4xl">
                            Get unforgettable pleasure with us.
                        </p>
                        <h1 className="font-myriad hero-lead mb-6 text-5xl leading-[1] font-bold uppercase md:text-5xl lg:text-[4rem]">
                            Let&apos;s travel <br /> together
                        </h1>
                        <p className="hero-text text-lg lg:text-xl leading-7 font-normal">
                            Exploring new cultures, creating unforgettable
                            memories, and sharing the adventure every step of
                            the way.
                        </p>
                        <div className="mt-8">
                            <a className="btn btn-primary btn-lg">
                                Explore Trek
                                <MdKeyboardDoubleArrowRight className="size-6" />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div
                className="top-0 right-0 flex h-full w-full items-center overflow-hidden max-md:relative md:absolute md:w-1/2 md:pl-[120px]">
                <HeroPackageSlider />
            </div>
            <button
                className="absolute top-1/2 left-1/2 size-[75px] -translate-x-1/2 -translate-y-1/2 cursor-pointer rounded-full text-white/50 shadow-2xl hover:text-white/75 hover:shadow-lg max-md:mt-0">
                <FaCirclePlay className="h-full w-full" />
            </button>
            {children}
        </section>
    );
}
