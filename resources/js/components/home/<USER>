import { blogs } from '@/data/dummy.js';
import { Link } from '@inertiajs/react';
import { FiExternalLink } from 'react-icons/fi';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import ArticleCard from '../blog/article-card.jsx';

export default function News() {
    return (
        <section>
            <div className="container px-4 py-8">
                <div className="py-4 text-center">
                    <p className="ff-montez mb-2 text-2xl text-primary md:text-3xl">
                        Do You Know
                    </p>
                    <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                        New & Articles
                    </h2>
                </div>
                <div className="my-4 flex flex-col md:flex-row md:gap-6">
                    <div className="w-full overflow-hidden rounded-2xl shadow-sm md:h-80 md:w-1/2">
                        <img
                            src="/assets/article-1.png"
                            alt="Article"
                            className="aspect-[5/3] w-full object-cover object-center md:aspect-[4/3] md:h-full"
                        />
                    </div>
                    <div className="overflow-hidden pt-4 md:w-1/2">
                        <Link href="/blog/a-day-in-bhaktapur-durbar-square">
                            <h3 className="ff-myrd-web mb-4 truncate text-2xl text-primary md:text-3xl">
                                A Day In Bhaktapur Durbar Square
                            </h3>
                        </Link>
                        <div className="max-md:text-md mb-6 text-primary max-sm:text-sm">
                            <p className="mb-5">
                                It was exactly 6 am in the morning when my
                                travel partner Upashana and I took the bus to
                                Bhaktapur from Chabahil for a day in Bhaktapur
                                Durbar Square.
                            </p>
                            <p>
                                Curious to know why we head out that early?
                                Well, you need to keep reading the blog till the
                                last, and I’ll try to keep it shorter and more
                                enjoyable.
                            </p>
                        </div>
                        <div className="md:text-md mt-4 flex justify-between text-sm">
                            <span className="italic">
                                <strong>Posted on:</strong> March 2024
                            </span>
                            <Link
                                href="/blog/a-day-in-bhaktapur-durbar-square"
                                className="inline-flex items-center gap-2"
                            >
                                View More
                                <FiExternalLink size={16} />
                            </Link>
                        </div>
                    </div>
                </div>
                <div className="grid gap-6 md:grid-cols-3">
                    {blogs.map((blog, index) => (
                        <ArticleCard
                            key={index}
                            title={blog.title}
                            imgSrc={blog.image}
                            content={blog.content}
                            url={blog.url}
                            imgClassName={'aspect-[5/3]'}
                        />
                    ))}
                </div>
                <div className="mt-8 flex justify-center">
                    <Link
                        herf="/blogs"
                        className="inline-flex cursor-pointer gap-1 rounded-full border border-[#004728] px-6 py-2 text-sm font-semibold text-[#004728] transition-colors duration-300 hover:bg-[#004728] hover:text-white"
                    >
                        View More
                        <MdKeyboardDoubleArrowRight className="size-5" />
                    </Link>
                </div>
            </div>
        </section>
    );
}
