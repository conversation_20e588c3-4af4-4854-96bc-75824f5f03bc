import { FiExternalLink } from 'react-icons/fi';
import PackageCard from '../package/package-card.jsx';
import { packages } from '../../data/dummy';

export default function PopularPackages() {

    return (
        <section className="relative pb-20 md:pb-50">
            <div className="bg-graphics absolute inset-0 z-[-1] flex items-center justify-center overflow-hidden">
                <img
                    src="/assets/bg-graphics-1.png"
                    alt="Backaground Graphics"
                    className="absolute bottom-0 left-0 -z-1 w-full object-cover"
                />
                <img
                    src="/assets/bg-graphics-3.png"
                    alt="Backaground Graphics"
                    className="absolute bottom-0 left-0 -z-1 w-full object-cover"
                />
            </div>
            <div className="container px-4 py-8">
                <div className="py-4">
                    <p className="ff-montez mb-2 text-2xl md:text-3xl">
                        Unique and Exclusive!
                    </p>
                    <div className="flex justify-between max-sm:flex-col">
                        <h2 className="ff-myrd-web mb-3 text-2xl sm:text-3xl md:text-5xl">
                            Popular Packages for 2025
                        </h2>
                        <a
                            href="#popular-packages"
                            className="inline-flex items-center gap-2 max-sm:text-sm"
                        >
                            View More
                            <FiExternalLink size={16} />
                        </a>
                    </div>
                </div>
                {/*<div className="grid gap-1 md:grid-cols-3 lg:-mx-4 xl:grid-cols-4">*/}
                <div className="grid grid-cols-[repeat(auto-fit,_minmax(275px,_1fr))] gap-1 lg:-mx-4">
                    {packages.length > 0 &&
                        packages.map((_package, index) => (
                            <PackageCard
                                key={index}
                                title={_package.title}
                                price={_package.price}
                                oldPrice={_package.oldPrice}
                                duration={_package.duration}
                                rating={_package.rating}
                                ratingCount={_package.ratingCount}
                                imgSrc={_package.imgSrc}
                                destination={_package.destination}
                                packageType={_package.packageType}
                            />
                        ))}
                </div>
            </div>
        </section>
    );
}
