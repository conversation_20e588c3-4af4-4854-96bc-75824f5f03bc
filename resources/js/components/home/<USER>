import { useState } from 'react';
import { TbSearch } from 'react-icons/tb';
import SelectSearch from '../ui/form-elements/select-search.jsx';

export default function SearchForm() {
    const mapIcon = `<svg class="shrink-0 size-5 text-gray-500"  xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor"  stroke-width="1.5"  stroke-linecap="round"  stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 18.5l-3 -1.5l-6 3v-13l6 -3l6 3l6 -3v7" /><path d="M9 4v13" /><path d="M15 7v5" /><path d="M21.121 20.121a3 3 0 1 0 -4.242 0c.418 .419 1.125 1.045 2.121 1.879c1.051 -.89 1.759 -1.516 2.121 -1.879z" /><path d="M19 18v.01" /></svg>`;

    const [destination, setDestination] = useState('Nepal');
    const [type, setType] = useState('Trekking');
    const [duration, setDuration] = useState('8 days');

    const handleDestinationChange = (e) => {
        setDestination(e.target.value);
    };

    const handleTypeChange = (e) => {
        setType(e.target.value);
    };

    const handleDurationChange = (e) => {
        setDuration(e.target.value);
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        alert(`Searching for ${type} in ${destination} for ${duration}`);
    };

    return (
        <div className="container">
            <form
                className="mx-auto w-[90%] md:w-[80%]"
                onSubmit={handleSubmit}
            >
                <div
                    className="rounded-5xl grid grid-cols-11 gap-4 border border-slate-200 bg-white/75 px-4 py-4 shadow-sm md:rounded-full md:px-6">
                    <div className="col-span-6 pl-2 sm:col-span-3">
                        <SelectSearch
                            label={'Destination'}
                            id="destination"
                            name="destination"
                            leftIcon={mapIcon}
                            onSelectChange={handleDestinationChange}
                            value={destination}
                        >
                            <option value="">Choose</option>
                            <option value="Bhutan">Bhutan</option>
                            <option value="Nepal">Nepal</option>
                            <option value="Tibet">Tibet</option>
                        </SelectSearch>
                    </div>

                    <div className="col-span-5 pl-2 sm:col-span-3">
                        <SelectSearch
                            label={'Type'}
                            id="type"
                            name="type"
                            onSelectChange={handleTypeChange}
                            value={type}
                        >
                            <option value="">Choose</option>
                            <option value="Trekking">Trekking</option>
                            <option value="Tour">Tour</option>
                            <option value="Visit">Visit</option>
                        </SelectSearch>
                    </div>

                    <div className="col-span-6 pl-2 sm:col-span-3">
                        <SelectSearch
                            label={'Duration'}
                            id="duration"
                            name="duration"
                            leftIcon={''}
                            onSelectChange={handleDurationChange}
                            value={duration}
                        >
                            <option value="">Choose</option>
                            <option value="6 days">6 days</option>
                            <option value="8 days">8 days</option>
                            <option value="12 days">12 days</option>
                        </SelectSearch>
                    </div>

                    <div className="col-span-5 flex items-center sm:col-span-2">
                        <button
                            type="submit"
                            className="btn btn-primary w-full rounded-full px-6"
                        >
                            Search
                            <TbSearch />
                        </button>
                    </div>
                </div>
            </form>
        </div>
    );
}
