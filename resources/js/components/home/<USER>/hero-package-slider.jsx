import HeroPackageSliderItem from './hero-package-slider-item.jsx';

import { SplideSlide } from '@splidejs/react-splide';
import '@splidejs/react-splide/css/core';
import DragButton from '../../ui/drag-button.jsx';
import DragSlider from '../../ui/drag-slider.jsx';

//import { Swiper, SwiperSlide } from 'swiper/react';
//import 'swiper/css';

export default function HeroPackageSlider() {
    const slides = [
        {
            imgUrl: '/assets/ebc-trek.png',
            imgAlt: 'EBC Trek',
            packageName: 'EBC Trek',
            destination: 'Nepal',
            scale: true
        },
        {
            imgUrl: '/assets/overland-tour.png',
            imgAlt: 'Overland Tour',
            packageName: 'Overland Tour',
            destination: 'Tibet',
            scale: false
        },
        {
            imgUrl: '/assets/tiger-nest-monastery.png',
            imgAlt: 'Tiger Nest Monastery',
            packageName: 'Tiger Nest Monastery',
            destination: 'Bhutan',
            scale: false
        }
    ];

    return (
        <>
            <div className="hero-slider flex w-full items-center justify-start gap-10 max-md:mt-16 max-md:mb-28">
                <DragSlider label="Hero Package Slider">
                    {slides.length > 0 &&
                        slides.map((slide, index) => (
                            <SplideSlide key={index}>
                                <HeroPackageSliderItem
                                    key={index}
                                    imgUrl={slide.imgUrl}
                                    imgAlt={slide.imgAlt}
                                    packageName={slide.packageName}
                                    destination={slide.destination}
                                    scale={false}
                                />
                            </SplideSlide>
                        ))}
                </DragSlider>
            </div>
            <DragButton />
        </>
    );
}
