export default function FloatingSelect({ id, name, children, label, border = true }) {

    const classList = ['peer p-4 pe-9 block w-full rounded-lg text-sm focus:border-green-500 focus:ring-green-500 disabled:opacity-50 disabled:pointer-events-none focus:pt-6 focus:pb-2 not-placeholder-shown:pt-6 not-placeholder-shown:pb-2 autofill:pt-6 autofill:pb-2'];

    if (border) {
        classList.push('border-gray-200');
    } else {
        classList.push('border-transparent');
    }

    return (
        <div className="relative">
            <select className={classList.join(' ')} id={id ?? name} name={name}>
                {children}
            </select>
            <label
                className="absolute top-0 start-0 p-4 h-full truncate pointer-events-none transition ease-in-out duration-100 border border-transparent peer-disabled:opacity-50 peer-disabled:pointer-events-none peer-focus:text-xs peer-focus:-translate-y-1.5 peer-focus:text-gray-500 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:-translate-y-1.5 peer-not-placeholder-shown:text-gray-500 text-gray-600">{label}</label>
        </div>
    );
}
