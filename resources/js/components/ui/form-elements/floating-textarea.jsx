import React from 'react';
import useHsTextareaAutoHeightInit from '@/hooks/preline/useHsTextareaAutoHeightInit';

export default function FloatingTextarea({ id, name, placeholder, label, border = true, rows = 3 }) {
    useHsTextareaAutoHeightInit();

    const classList = ['peer p-4 block w-full bg-white rounded-lg sm:text-sm placeholder:text-transparent focus:border-green-600 focus:ring-green-600 disabled:opacity-50 disabled:pointer-events-none focus:pt-6 focus:pb-2 not-placeholder-shown:pt-6 not-placeholder-shown:pb-2 autofill:pt-6 autofill:pb-2'];

    if (border) {
        classList.push('border-gray-200');
    } else {
        classList.push('border-transparent');
    }

    return (
        <div className="relative">
            <textarea
                id={id ?? name}
                name={name}
                className={classList.join(' ')}
                placeholder={label ?? placeholder}
                data-hs-textarea-auto-height="true"
                rows={rows}
            ></textarea>
            <label
                htmlFor={id ?? name}
                className="absolute top-0 start-0 p-4 h-full text-sm truncate pointer-events-none transition ease-in-out duration-100 border border-transparent origin-[0_0] peer-disabled:opacity-50 peer-disabled:pointer-events-none peer-focus:text-xs peer-focus:-translate-y-1.5 peer-focus:text-gray-500 text-gray-600 peer-not-placeholder-shown:text-xs peer-not-placeholder-shown:-translate-y-1.5 peer-not-placeholder-shown:text-gray-500"
            >{label}</label>
        </div>
    );
}

