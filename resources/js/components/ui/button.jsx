import { twMerge } from 'tailwind-merge';

/**
 *
 * @param children
 * @param type
 * @param variant - 'primary', 'secondary', 'accent', 'info', 'success', 'warning', 'error'.
 * @param size - 'xs', 'sm', 'md', 'lg', 'xl'
 * @param block
 * @param wide
 * @param props
 * @returns {Element}
 * @constructor
 */
export default function Button({ children, type = 'submit', variant, size, block, wide, outline, className, ...props }) {
    const classList = ['btn'];

    if (variant) {
        classList.push(`btn-${variant}`);
    } else {
        classList.push('btn-neutral');
    }

    if (size) {
        classList.push(`btn-${size}`);
    }

    if (block) {
        classList.push('btn-block');
    }

    if (wide) {
        classList.push('btn-wide');
    }

    if (outline) {
        classList.push('btn-outline');
    }

    return (
        <button
            className={twMerge(classList.join(' '), className)}
            type={type}
            {...props}
        >
            {children}
        </button>
    );
}
