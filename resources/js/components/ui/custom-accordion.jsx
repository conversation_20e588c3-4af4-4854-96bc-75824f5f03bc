import { useEffect, useState } from "react";
import { TbMinus , TbPlus } from "react-icons/tb";

export default function CustomAccordion({data}) {
    const [active, setActive] = useState(Array(data.length).fill(false));

    const handleToggle = (index) => {
        setActive((prevActive) => {
            return prevActive.map((active, i) => i === index ? !active : false);
        });
    }

    useEffect(() => {
        setActive((prevActive) => {
            prevActive[0] = true;
            return [...prevActive];
        });
    }, [])

    return (
        <div className="mt-3">
            {data.map((item, index) => (
                <div className="relative group/item ms-4 pl-8 border-l-2 border-primary transition duration-300" key={index}>
                    <div className={`flex gap-3 ${active[index] ? 'pb-3' : (index+1 == active.length ? 'pb-0' : 'pb-6')} items-center`}>
                        <div
                            className={`text-lg font-semibold transition duration-300 ${active[index] ? '' : 'truncate'}`}
                            onClick={() => handleToggle(index)}
                        >
                            Day {item.day}: {item.title}
                        </div>
                        <div>
                            <button
                                type="button"
                                className="rounded-full bg-primary p-[6px] text-white transition duration-300"
                                onClick={() => handleToggle(index)}
                            >
                                {active[index] ? <TbMinus className="size-6"/> : <TbPlus className="size-6"/> }
                            </button>
                        </div>
                    </div>
                    <div className={`pb-6 ${active[index] ? '' : 'hidden'} text-sm font-medium leading-6 transition duration-300`}>{item.description}</div>
                    <div className="absolute top-0 -left-[1.125rem] z-3">
                        <span className="inline-block rounded-full bg-primary p-2 text-white">{item.icon}</span>
                    </div>
                </div>
            ))}
        </div>
    )
}
