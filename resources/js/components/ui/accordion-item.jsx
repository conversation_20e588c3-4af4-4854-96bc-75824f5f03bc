import { FiChevronDown, FiChevronUp } from 'react-icons/fi';

function AccordionItem({ active, id, title, content }) {
    return (
        <div
            className={`hs-accordion ${active ? 'active' : ''} hs-accordion-active:border-green-900 bg-gray-200 hs-accordion-active:bg-green-900 hover:bg-green-900 hs-accordion-active:text-white border border-gray-200 rounded-xl mb-4`}
            id={id + '-heading'}
        >
            <button
                className="hs-accordion-toggle hs-accordion-active:text-white inline-flex justify-between items-center gap-x-3 w-full font-semibold text-start text-gray-800 py-4 px-5 hover:text-white disabled:opacity-50 disabled:pointer-events-none"
                aria-expanded="false"
                aria-controls={id + '-content'}
            >
                {title}
                <FiChevronDown className="hs-accordion-active:hidden block size-4" />
                <FiChevronUp className="hs-accordion-active:block hidden size-4" />
            </button>
            <div
                id={id + '-content'}
                className="hs-accordion-content w-full hidden overflow-hidden transition-[height] duration-300"
                role="region"
                aria-labelledby={id + '-heading'}
            >
                <div className="pb-4 px-5">
                    <div className="hs-accordion-active:text-white" dangerouslySetInnerHTML={{__html: content}}/>
                </div>
            </div>
        </div>
    );
}

export default AccordionItem;
