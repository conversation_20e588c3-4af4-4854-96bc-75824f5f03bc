import { Splide } from '@splidejs/react-splide';

export default function DragSlider({
    label,
    children,
    perPage = 2.7,
    gap = '1rem',
    interval = 5000,
    isCentered = false,
    autoplay = true,
}) {
    const configOptions = {
        type: 'loop',
        perPage: perPage,
        perMove: 1,
        drag: true,
        snap: true,
        pagination: false,
        arrows: false,
        autoplay: autoplay,
        interval: interval,
        autoHeight: true,
        autoWidth: true,
        gap: gap,
        updateOnMove: true,
        responsive: {
            640: {
                perPage: 1.5,
                focus: 'center',
            },
        },
    };

    if (isCentered) {
        configOptions.focus = 'center';
    }

    return (
        <>
            <Splide
                aria-label={label}
                options={configOptions}
                className="h-full w-full"
            >
                {children}
            </Splide>
        </>
    );
}
