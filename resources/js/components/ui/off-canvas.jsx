import { useHsOverlayAutoInit } from '@/hooks/index.js';
import { BsXLg } from 'react-icons/bs';

export default function OffCanvas({ idRef }) {
    useHsOverlayAutoInit();

    return (
        <>
            <div
                id={idRef}
                className="hs-overlay hs-overlay-open:translate-x-0 fixed end-0 top-0 z-80 hidden h-full w-full max-w-xs translate-x-full transform border-e border-gray-200 bg-white transition-all duration-300"
                role="dialog"
                tabIndex="-1"
                aria-labelledby={idRef + '-label'}
            >
                <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3">
                    <h3
                        id={idRef + '-label'}
                        className="font-bold text-gray-800"
                    >
                        Package Filter
                    </h3>
                    <button
                        type="button"
                        className="inline-flex size-8 items-center justify-center gap-x-2 rounded-full border border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 focus:bg-gray-200 focus:outline-hidden disabled:pointer-events-none disabled:opacity-50"
                        aria-label="Close"
                        data-hs-overlay={'#' + idRef}
                    >
                        <span className="sr-only">Close</span>
                        <BsXLg className="size-4 shrink-0" />
                    </button>
                </div>
                <div className="p-4">
                    <p className="text-gray-800">
                        Some text as placeholder. In real life you can have the
                        elements you have chosen. Like, text, images, lists,
                        etc.
                    </p>
                </div>
            </div>
        </>
    );
}
