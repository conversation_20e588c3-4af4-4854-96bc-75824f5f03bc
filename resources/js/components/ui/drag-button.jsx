import { BsChevronLeft, BsChevronRight } from 'react-icons/bs';
import { twMerge } from "tailwind-merge";

export default function DragButton({className}){
    return (
        <div className={twMerge("shadow-edge absolute top-1/2 left-1/2 flex h-[75px] w-[75px] -translate-x-1/4 -translate-y-1/2 items-center justify-center rounded-full bg-black/75 text-white", className)}>
            <BsChevronLeft />
            <span className="uppercase">DRAG</span>
            <BsChevronRight />
        </div>
    );
}
