import { Link } from '@inertiajs/react';
import { LuHouse } from 'react-icons/lu';

function Breadcrumb({ links }) {
    return (
        <div className="breadcrumbs text-sm">
            <ul>
                <li className="before:text-gray-100">
                    <Link href="/">
                        <LuHouse className={'size-4'} />
                        <span>Home</span>
                    </Link>
                </li>
                {links.map((link, index) => {
                    if (link.href) {
                        return (
                            <li className="before:text-gray-100" key={index}>
                                <Link href={link.href}>{link.title}</Link>
                            </li>
                        );
                    }

                    return (
                        <li key={index} aria-disabled={true}>
                            {link.title}
                        </li>
                    );
                })}
            </ul>
        </div>
    );
}

export default Breadcrumb;
