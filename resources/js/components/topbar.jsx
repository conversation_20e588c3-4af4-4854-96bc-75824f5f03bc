import { Link } from '@inertiajs/react';
import { LuClock2 } from 'react-icons/lu';
import { PiMapPin } from 'react-icons/pi';

export default function Topbar() {
    return (
        <>
            <div className="flex justify-between gap-2 bg-white py-1 md:py-2">
                <div className="flex flex-col gap-[2px] px-2 text-gray-500 md:flex-row md:items-center md:gap-2 md:px-4">
                    <div className="inline-flex items-center gap-1 md:gap-2">
                        <PiMapPin className={'size-4 md:size-5'} />
                        <span className="text-[10px] leading-none md:text-xs">
                            Boudha 5, Kathmandu, Nepal
                        </span>
                    </div>
                    <div className="hidden h-full min-h-4 border-r border-gray-400 md:block"></div>
                    <div className="inline-flex items-center gap-1 md:gap-2">
                        <LuClock2 className={'size-4 md:size-5'} />
                        <span className="text-[10px] leading-none md:text-xs">
                            Sun to Friday: 8:00 am - 7:00 pm
                        </span>
                    </div>
                </div>
                <div className="flex items-center justify-center gap-2 px-4 text-gray-500">
                    <Link
                        className="text-[10px] leading-none md:text-xs"
                        href="/faqs"
                    >
                        FAQ
                    </Link>
                    <div className="h-4 border-r border-gray-400 md:h-full md:min-h-4"></div>
                    <Link
                        className="text-[10px] leading-none md:text-xs"
                        href="/support"
                    >
                        Support
                    </Link>
                </div>
            </div>
        </>
    );
}
