import { menuItems } from '@/data/dummy';
import { Link } from '@inertiajs/react';
import { AiOutlineMenu } from 'react-icons/ai';
import { MdKeyboardDoubleArrowRight } from 'react-icons/md';
import Logo from '../../assets/everest-logo.png';
import Navigation from './navigation.jsx';

export default function Header() {
    return (
        <>
            <div className="navbar bg-base-100/75 shadow-lg">
                <div className="navbar-start">
                    <div className="dropdown">
                        <div
                            tabIndex={0}
                            role="button"
                            className="cursor-pointer px-4 lg:hidden"
                        >
                            <AiOutlineMenu className={'size-5'} />
                        </div>
                        <ul
                            tabIndex={0}
                            className="menu menu-sm dropdown-content bg-base-100 rounded-box z-1 mt-3 w-52 p-2 shadow"
                        >
                            <Navigation menuItems={menuItems} />
                        </ul>
                    </div>
                    <Link href={'/'} className="px-3">
                        <img src={Logo} alt="" width={120} />
                    </Link>
                </div>
                <div className="navbar-center hidden lg:flex">
                    <ul className="menu menu-horizontal px-1">
                        <Navigation menuItems={menuItems} />
                    </ul>
                </div>
                <div className="navbar-end px-3">
                    <Link herf="/customized-trip" className="btn btn-primary">
                        Book Now
                        <MdKeyboardDoubleArrowRight className="size-4" />
                    </Link>
                </div>
            </div>
        </>
    );
}
