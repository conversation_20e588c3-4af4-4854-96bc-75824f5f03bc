import ArticleCard from '@/components/blog/article-card.jsx';
import Breadcrumb from '../components/shared/breadcrumb.jsx';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import { blogs } from '../data/dummy';
import AppLayout from '../layouts/app-layout.jsx';

export default function Destinations({ title }) {
    const breadcrumbLinks = [{ title: 'Blogs' }];

    const blogList = [...blogs, ...blogs, ...blogs];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 pt-8 pb-18">
                    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        {blogList.map((blog, index) => (
                            <ArticleCard
                                key={index}
                                title={blog.title}
                                imgSrc={blog.image}
                                content={blog.content}
                                url={blog.url}
                                imgClassName={'aspect-[5/3]'}
                            />
                        ))}
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
