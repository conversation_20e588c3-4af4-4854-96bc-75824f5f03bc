import Button from '@/components/ui/button.jsx';
import FloatingInput from '@/components/ui/form-elements/floating-input.jsx';
import FloatingTextarea from '@/components/ui/form-elements/floating-textarea.jsx';
import { PiHeadset, PiMapPinArea, PiPaperPlaneTilt } from 'react-icons/pi';
import Breadcrumb from '../components/shared/breadcrumb.jsx';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import AppLayout from '../layouts/app-layout.jsx';

function InfoCard({ Icon, title, children }) {
    return (
        <div className="rounded-xl bg-zinc-100 p-6">
            <div>
                <span className="inline-block rounded-full bg-green-700 p-5">
                    <Icon className="size-8 text-white" />
                </span>
            </div>
            <h3 className="mt-4 text-xl font-bold">{title}</h3>
            <div className="text-md mt-2 font-medium text-gray-400">
                {children}
            </div>
        </div>
    );
}

export default function Contact({ title }) {
    const breadcrumbLinks = [{ title: 'Contact', href: '/contact' }];

    const Anchor = ({ href, children }) => (
        <p>
            <a href={href}>{children}</a>
        </p>
    );

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 pt-8 pb-18">
                    <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                        <InfoCard Icon={PiPaperPlaneTilt} title="Email Address">
                            <Anchor href="mailto:<EMAIL>">
                                <EMAIL>
                            </Anchor>
                            <Anchor href="mailto:<EMAIL>">
                                <EMAIL>
                            </Anchor>
                        </InfoCard>
                        <InfoCard Icon={PiHeadset} title="Call Us For Support">
                            <Anchor href="tel:+0123145678">
                                +************
                            </Anchor>
                            <Anchor href="tel:+0123145678">
                                +************
                            </Anchor>
                        </InfoCard>
                        <InfoCard Icon={PiMapPinArea} title="Our Head Office">
                            <p>
                                70 Washington Square South, New York, NY 10012,
                                US
                            </p>
                        </InfoCard>
                    </div>
                </section>
                <section className="min-h-[550px] w-full bg-gray-100">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d56516.27610848991!2d85.284933209783!3d27.709030241502457!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x39eb198a307baabf%3A0xb5137c1bf18db1ea!2sKathmandu%2044600%2C%20Nepal!5e0!3m2!1sen!2sbd!4v1752061594541!5m2!1sen!2sbd"
                        width="100%"
                        height="550"
                        style={{ border: 0 }}
                        frameBorder="0"
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                    ></iframe>
                </section>
                <section className="container mb-20">
                    <div className="relative z-9999 mx-auto -mt-50 w-[90%] rounded-xl bg-zinc-100 p-8 shadow-sm">
                        <div className="mx-auto text-center sm:w-[60%] md:w-[50%]">
                            <h2 className="mb-3 text-3xl font-semibold">
                                Get in touch
                            </h2>
                            <p className="text-sm text-gray-500">
                                Get in touch for personalized assistance. We're
                                here to help and provide solutions tailored to
                                your requirements.
                            </p>
                        </div>
                        <form className="mt-8" onSubmit={() => {}}>
                            <div className="grid gap-6 sm:grid-cols-1">
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="First Name*"
                                        name="first_name"
                                        border={false}
                                    />
                                    <FloatingInput
                                        label="Last Name*"
                                        name="last_name"
                                        border={false}
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="Phone*"
                                        name="phone"
                                        border={false}
                                    />
                                    <FloatingInput
                                        label="Email*"
                                        name="email"
                                        border={false}
                                    />
                                </div>
                                <div className="">
                                    <FloatingTextarea
                                        label="Message*"
                                        name="message"
                                        border={false}
                                    />
                                </div>
                                <div className="">
                                    <Button variant="primary" size="lg" block>
                                        Send Message
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
