import DestinationCard from '../components/package/destination-card.jsx';
import Breadcrumb from '../components/shared/breadcrumb.jsx';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import AppLayout from '../layouts/app-layout.jsx';

export default function Destinations({ title }) {
    const breadcrumbLinks = [{ title }];
    const destinations = [
        {
            name: 'Nepal',
            slug: 'nepal',
            image: '/assets/destinations/nepal.jpg',
            packages: 150
        },
        {
            name: 'India',
            slug: 'india',
            image: '/assets/destinations/india.jpg',
            packages: 90
        },
        {
            name: 'Bhutan',
            slug: 'bhutan',
            image: '/assets/destinations/bhutan.jpg',
            packages: 50
        },
        {
            name: 'Tibet',
            slug: 'tibet',
            image: '/assets/destinations/tibet.jpg',
            packages: 10
        }
    ];

    return (
        <>
            <AppLayout title="Activities">
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container mt-8 px-4 py-12">
                    <div className="grid gap-6 sm:grid-cols-2">
                        {destinations.map((destination) => (
                            <DestinationCard
                                key={destination.slug}
                                name={destination.name}
                                image={destination.image}
                                slug={destination.slug}
                                packages={destination.packages}
                            />
                        ))}
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
