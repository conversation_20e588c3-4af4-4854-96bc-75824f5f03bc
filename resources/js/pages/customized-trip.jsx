import Breadcrumb from '@/components/shared/breadcrumb.jsx';
import HeroTopSection from '@/components/shared/hero-top-section.jsx';
import Button from '@/components/ui/button.jsx';
import FloatingDatepicker from '@/components/ui/form-elements/floating-datepicker.jsx';
import FloatingInput from '@/components/ui/form-elements/floating-input.jsx';
import FloatingSelect from '@/components/ui/form-elements/floating-select.jsx';
import FloatingTextarea from '@/components/ui/form-elements/floating-textarea.jsx';
import AppLayout from '@/layouts/app-layout';

export default function CustomizedTrip({ title }) {
    const breadcrumbLinks = [{ title }];

    return (
        <AppLayout title={title}>
            <HeroTopSection title={title}>
                <Breadcrumb links={breadcrumbLinks} />
            </HeroTopSection>
            <section className="container px-4 pt-8 pb-18">
                <div className="card bg-base-100 card-md border border-gray-100 shadow-sm">
                    <div className="card-body">
                        <form className="my-8" onSubmit={() => {}}>
                            <div className="grid gap-6 sm:grid-cols-1">
                                <h3 className="text-lg font-semibold text-primary">
                                    Personal Information
                                </h3>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="Full Name*"
                                        name="full_name"
                                    />
                                    <FloatingInput
                                        label="Email Address*"
                                        name="email_address"
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingInput
                                        label="Country Code + Phone*"
                                        name="phone"
                                    />
                                    <FloatingSelect
                                        label="Country*"
                                        name="country"
                                    >
                                        <option value="">Choose country</option>
                                        <option value="Nepal">Nepal</option>
                                        <option value="India">India</option>
                                        <option value="Bhutan">Bhutan</option>
                                        <option value="China">China</option>
                                    </FloatingSelect>
                                </div>
                                <h3 className="text-lg font-semibold text-primary">
                                    Trip Details
                                </h3>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <FloatingSelect
                                        label="Choose a package*"
                                        name="country"
                                    >
                                        <option value="Langtang Vally Trek">
                                            Langtang Vally Trek
                                        </option>
                                        <option value="Everest Trek">
                                            Everest Trek
                                        </option>
                                    </FloatingSelect>
                                    <FloatingDatepicker
                                        label="Approx. Date of Travel"
                                        name="travel_date"
                                    />
                                </div>
                                <div className="grid gap-6 sm:grid-cols-1 md:grid-cols-2">
                                    <div className="grid gap-6 sm:grid-cols-2">
                                        <FloatingInput
                                            type="number"
                                            label="No. of Adults*"
                                            name="adults"
                                        />
                                        <FloatingInput
                                            type="number"
                                            label="No. of Children*"
                                            name="children"
                                        />
                                    </div>
                                    <FloatingSelect
                                        label="Choose Group Size*"
                                        name="group_size"
                                    >
                                        <option value="Group of 1-2">
                                            Group of 1-2
                                        </option>
                                        <option value="Group of 2-5">
                                            Group of 2-5
                                        </option>
                                        <option value="Group of 5-10">
                                            Group of 5-10
                                        </option>
                                    </FloatingSelect>
                                </div>
                                <div className="">
                                    <FloatingTextarea
                                        label="More Information*"
                                        name="message"
                                        rows={6}
                                    />
                                </div>
                                <div className="">
                                    <label className="label">
                                        <input
                                            type="checkbox"
                                            className="checkbox checkbox-primary"
                                        />
                                        I accept{' '}
                                        <a
                                            href="/terms"
                                            className="text-primary"
                                        >
                                            terms and conditions
                                        </a>
                                    </label>
                                </div>
                                <div className="card-actions">
                                    <Button
                                        variant="primary"
                                        size="md"
                                        className="px-12"
                                    >
                                        Submit
                                    </Button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </section>
        </AppLayout>
    );
}
