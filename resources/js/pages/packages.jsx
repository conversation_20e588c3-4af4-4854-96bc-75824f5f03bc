import { LuListFilter } from 'react-icons/lu';
import PackageCard from '../components/package/package-card.jsx';
import Breadcrumb from '../components/shared/breadcrumb';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import OffCanvas from '../components/ui/off-canvas.jsx';
import { packages } from '../data/dummy';
import AppLayout from '../layouts/app-layout.jsx';

export default function Packages({ title, destination, category }) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        { title: 'Nepal', href: `/activities/${destination}` },
        {
            title: 'Trekking In Nepal',
            href: `/activities/${destination}/${category}`
        },
        { title: 'Everest Treks' }
    ];

    const packageList = [...packages, ...packages];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 py-12">
                    <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                        <p className="mb-8">
                            Trekking Nepal Nepal was NepalIt was exactly 6 am in
                            the morning when my travel partner Upashana and I
                            took the bus to Bhaktapur from Chabahil for a day in
                            Bhaktapur Durbar Square. Curious to know why we head
                            out that early? Well, you need to keep reading the
                            blog till the last, and I’ll try to keep it shorter
                            and more enjoyable.
                        </p>
                        <p className="mb-0">
                            Nepal was exactly 6 am in the morning Nepal when my
                            travel partner Upashana and I took the bus to
                            Bhaktapur from Chabahil for a day in Bhaktapur
                            Durbar Square. Curious to know why we head out that
                            early? Well, you need to keep reading the blog till
                            the last, and I’ll try to keep it shorter and more
                            enjoyable Nepal.
                        </p>
                    </div>
                </section>
                <section className="container px-4 pt-8 pb-18">
                    <div className="py-4 text-center">
                        <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                            Packages
                        </h2>
                    </div>
                    <div className="mb-2 flex justify-between">
                        <p className="text-md font-medium">
                            Showing {packageList.length ?? 0} Packages
                        </p>
                        <div className="text-md font-medium">
                            <button
                                className="flex cursor-pointer items-center gap-1"
                                type="button"
                                aria-haspopup="dialog"
                                aria-expanded="false"
                                aria-controls="filter-offcanvas"
                                data-hs-overlay="#filter-offcanvas"
                            >
                                <LuListFilter className="size-5" />{' '}
                                <span>Filter</span>
                            </button>
                        </div>
                    </div>
                    <div className="grid grid-cols-[repeat(auto-fit,_minmax(275px,_1fr))] gap-1 lg:-mx-4">
                        {packageList.map((_package, index) => (
                            <PackageCard
                                key={index}
                                {..._package}
                                href={`/packages/${destination}/${category}/${_package.title.toLocaleLowerCase().replaceAll(' ', '-')}`}
                                forSlider={false}
                            />
                        ))}
                    </div>
                </section>
            </AppLayout>

            <OffCanvas idRef="filter-offcanvas" />
        </>
    );
}
