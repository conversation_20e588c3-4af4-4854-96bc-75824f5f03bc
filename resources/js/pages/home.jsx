import AppLayout from '../layouts/app-layout.jsx';
import BestOffers from '../components/home/<USER>';
import HeroSection from '../components/home/<USER>';
import News from '../components/home/<USER>';
import PlanYourTrip from '../components/home/<USER>';
import PopularPackages from '../components/home/<USER>';
import ReviewAndBrands from '../components/home/<USER>';
import SearchForm from '../components/home/<USER>';
import SolabansVillage from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import TopDestinations from '../components/home/<USER>';
import TravelExperience from '../components/home/<USER>';

export default function Home() {
    return (
        <>
            <AppLayout title="Home" containerClass="home-page">
                <HeroSection />
                <div className="relative -mt-[40px] mb-8 md:-mt-[70px] xl:-mt-10">
                    <SearchForm />
                </div>
                <TopDestinations />
                <PlanYourTrip />
                <BestOffers />
                <PopularPackages />
                <TravelExperience />
                <SolabansVillage />
                <Testimonials />
                <ReviewAndBrands />
                <News />
            </AppLayout>
        </>
    );
}
