import Breadcrumb from '@/components/shared/breadcrumb.jsx';
import HeroTopSection from '@/components/shared/hero-top-section.jsx';
import LiveSupportCard from '@/components/shared/live-support-card.jsx';
import Accordion from '@/components/ui/accordion.jsx';
import AppLayout from '@/layouts/app-layout.jsx';
import { FiArrowRight } from 'react-icons/fi';
import Button from '../components/ui/button';
import FloatingInput from '../components/ui/form-elements/floating-input';
import FloatingTextarea from '../components/ui/form-elements/floating-textarea';
import { faqs } from '../data/dummy';

export default function FaqPage({ title }) {
    const breadcrumbLinks = [{ title }];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container mt-8 px-4 pt-8 pb-18">
                    <div className="grid gap-8 sm:grid-cols-1 md:grid-cols-3">
                        <div className="md:col-span-2">

                            {faqs.map((group, index) => (
                                <div className='mb-8'>
                                    <h2 className="mb-4 text-2xl font-semibold md:text-3xl">
                                        {group.title}
                                    </h2>
                                    <Accordion id={group.id} items={group.items} key={index} />
                                </div>
                            ))}

                        </div>
                        <div className="md:col-span-1">
                            <div className="w-full">
                                <div className="mb-6">
                                    <h3 className="mb-1 text-xl font-montez text-green-600 font-medium">
                                        Need Any Help?
                                        </h3>
                                    <h2 className="mb-4 text-3xl font-medium">Popular Inquiries</h2>
                                    <p className="text-gray-400 text-sm">
                                        If you need immediate assistance, click the button below to chat with our support team.
                                    </p>
                                </div>

                                <div className="bg-gray-200 rounded-xl px-4 py-6 mb-6">
                                    <h3 className="mb-4 text-lg font-medium">
                                        Have any Question?
                                    </h3>
                                    <div className='flex flex-col gap-4'>
                                        <FloatingInput label="Your Name" type="text" name="name" />
                                        <FloatingInput label="Your Email" type="email" name="email" />
                                        <FloatingTextarea label="Your Message" type="text" name="message" />
                                        <div>
                                            <Button size="lg" className="py-6 rounded-lg bg-green-600 border-green-600 text-white font-normal text-[1rem]" block>
                                                Ask Question Now
                                                <FiArrowRight />
                                            </Button>
                                        </div>
                                    </div>
                                </div>

                                <LiveSupportCard/>
                            </div>
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
