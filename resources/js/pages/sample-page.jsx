import Breadcrumb from '../components/shared/breadcrumb.jsx';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import AppLayout from '../layouts/app-layout.jsx';

export default function SamplePage({ title }) {
    const breadcrumbLinks = [{ title }];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 pt-8 pb-18">
                    <div className="hidden h-auto w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg">
                        <img
                            src="/assets/blog-1.jpg"
                            alt=""
                            className="h-full w-full object-cover"
                        />
                    </div>
                    <div className="mx-auto mt-4 py-6 lg:w-9/10">
                        <h1 className="mt-8 text-3xl font-semibold md:text-4xl">
                            Sample Page Title
                        </h1>
                        <div className="blog-content text-md mt-6 md:text-lg [&_div]:mb-4 [&_p]:mb-4 [&_p]:leading-8 [&_p]:font-medium [&_p]:text-slate-800 [&>h2]:mt-6 [&>h2]:mb-2 [&>h2]:text-2xl [&>h2]:font-[600] [&>h3]:mt-4 [&>h3]:mb-2 [&>h3]:text-xl [&>h3]:font-[600]">
                            <p>
                                Vast numbers of employees now work remotely, and
                                it is too late to develop a set of remote-work
                                policies if you did not already have one. But
                                there are ways to make the remote-work
                                experience productive and engaging — for
                                employees and the organisation.
                            </p>
                            <h2>An example heading at level 2</h2>
                            <p>
                                Use both direct conversations and indirect
                                observations to get visibility into employees’
                                challenges and concerns. Use every opportunity
                                to make clear to employees that you support and
                                care for them. To facilitate regular
                                conversations between managers and employees,
                                provide managers with guidance on how best to
                                broach sensitive subjects arising from the
                                COVID-19 pandemic, including alternative work
                                models, job security and prospects, impact on
                                staffing and tension in the workplace.
                            </p>
                            <p>
                                Use both direct conversations and indirect
                                observations to get visibility into employees’
                                challenges and concerns. Use every opportunity
                                to make clear to employees that you support and
                                care for them. To facilitate regular
                                conversations between managers and employees,
                                provide managers with guidance on how best to
                                broach sensitive subjects arising from the
                                COVID-19 pandemic, including alternative work
                                models, job security and prospects, impact on
                                staffing and tension in the workplace.
                            </p>
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
