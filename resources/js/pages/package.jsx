import LiveSupportCard from "@/components/shared/live-support-card";
import Accordion from "@/components/ui/accordion";
import { FaGlobeAsia, FaPlay, FaStar } from "react-icons/fa";
import { GiMountainRoad } from "react-icons/gi";
import { IoMdShare } from "react-icons/io";
import { PiMapPin } from "react-icons/pi";
import { TbCalendar, TbCalendarClock } from "react-icons/tb";
import { CgArrowLongRight } from "react-icons/cg";
import Breadcrumb from "../components/shared/breadcrumb";
import HeroTopSection from "../components/shared/hero-top-section";
import Button from "../components/ui/button";
import { faqs } from '../data/dummy';
import AppLayout from "../layouts/app-layout";
import CustomAccordion from "@/components/ui/custom-accordion";
import Icon from "@/components/ui/icon";
import PackageReply from "@/components/package/pacakge-reply";
import ReviewRatingInput from "@/components/package/review-rating-input";
import FloatingInput from "@/components/ui/form-elements/floating-input";
import FloatingTextarea from "@/components/ui/form-elements/floating-textarea";

export default function Package({ title, destination, category, slug }) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        { title: 'Nepal', href: `/activities/${destination}` },
        {
            title: 'Trekking In Nepal',
            href: `/activities/${destination}/${category}`
        },
        {
            title: 'Everest Treks',
            href: `/packages/${destination}/${category}`
        },
        {
            title: 'Cho La Pass Trek',
        }
    ];

    const prices = [
        {
            title: '1 pax',
            price: 1799
        },
        {
            title: '2 pax',
            price: 1599
        },
        {
            title: '3 - 5 pax',
            price: 1499
        },
        {
            title: '6 - 10 pax',
            price: 1399
        },
        {
            title: '11 - 15 pax',
            price: 1330
        },
        {
            title: '16 - 20 pax',
            price: 1255
        },
        {
            title: '21 - 30 pax',
            price: 1100
        }
    ];

    const defaultPlan = {
        title: "Arrival in Kathmandu, you'll be received by our airport representative and transfer to the hotel",
        description: "Welcome to Nepal! When your flight arrives at the Tribhuvan International Airport, a representative from Alpine Ramble Treks will be waiting to meet you. After clearing customs, we’ll whisk you away to your hotel in the city. Kathmandu lies at approximately 1,300 meters above sea level, so you don’t need to spend much time here for acclimatization purposes before heading off into the mountains. But no trek in Nepal would be complete without sampling the intoxicating sights and sounds of the country’s capital city. From the crowded bazaars of the Thamel district to the hilltop stupa of Swayambhunath, Kathmandu is a feast for the senses. You’ll be awestruck at this urban maze of crowded streets, bustling markets, scenic hilltops, and Buddhist and Hindu shrines on every street corner. Depending on the time of your arrival, you may have some time to explore the city before returning to your hotel for the night.",
        icon: <Icon name={'trekking'} className="size-4.5" />,
    };

    const planData = [
        {
            day: 1,
            ...defaultPlan,
            icon: <Icon name={'plane'} className="size-4.5"/>
        },
        {
            day: 2,
            ...defaultPlan
        },
        {
            day: 3,
            ...defaultPlan
        },
        {
            day: 4,
            ...defaultPlan
        },
        {
            day: 5,
            ...defaultPlan
        },
    ];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 pt-8 pb-18">
                    <div className="h-auto w-full overflow-hidden rounded-xl bg-gray-100 shadow-lg">
                        <img
                            src="/assets/blog-1.jpg"
                            alt=""
                            className="h-full w-full object-cover"
                        />
                    </div>
                    <div className="w-full">
                        <div className="flex flex-col sm:flex-row mt-6 sm:mt-8 gap-4 sm:justify-between">
                            <div className="flex flex-col">
                                <h1 className="mb-3 text-2xl sm:text-3xl font-semibold md:text-4xl text-primary">
                                    Everent Cho La Pass Trek
                                </h1>
                                <div className="flex flex-col gap-2 sm:flex-row sm:gap-6">
                                    <div className="inline-flex shrink-0 items-center gap-2">
                                        <span className="text-sm sm:text-md font-medium">
                                            50 review
                                        </span>
                                        <div className="flex gap-1">
                                            <FaStar className="size-4 sm:size-5" />
                                            <FaStar className="size-4 sm:size-5" />
                                            <FaStar className="size-4 sm:size-5" />
                                            <FaStar className="size-4 sm:size-5" />
                                            <FaStar className="size-4 sm:size-5" />
                                        </div>
                                    </div>
                                    <div className="inline-flex shrink-0 items-center gap-2">
                                        <PiMapPin className="size-4 sm:size-5 text-gray-800" />
                                        <span className="text-sm sm:text-md font-medium">
                                            Solukhumbu, Nepal
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div className="flex item-center">
                                <Button outline>
                                    <span>Share</span>
                                    <IoMdShare className="size-4"/>
                                </Button>
                            </div>
                        </div>
                        <div className="my-6 py-4 border-y border-gray-300 grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div className="flex items-center gap-3">
                                <span className="inline-block p-3 rounded-full border-1">
                                    <TbCalendarClock className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs leading-[1.3] font-medium text-gray-600">Duration</span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        12 days
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="inline-block p-3 rounded-full border-1">
                                    <FaGlobeAsia className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs leading-[1.3] font-medium text-gray-600">Country</span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        Nepal
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="inline-block p-3 rounded-full border-1">
                                    <GiMountainRoad className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs leading-[1.3] font-medium text-gray-600">
                                        Maximum Altitude
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        5545m (Kalapathar)
                                    </strong>
                                </span>
                            </div>
                            <div className="flex items-center gap-3">
                                <span className="inline-block p-3 rounded-full border-1">
                                    <TbCalendar className="size-6" />
                                </span>
                                <span className="flex flex-col">
                                    <span className="text-xs leading-[1.2] font-medium text-gray-600">
                                        Best Time
                                    </span>
                                    <strong className="text-md font-semibold leading-[1.2]">
                                        March - Jun, Sep - Dec
                                    </strong>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-0 md:gap-8 [&>div]:mb-4">
                        <div className="col-span-2">
                            {/** Overview */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Overview</h2>
                                <div className="[&_p]:mb-4 text-md text-gray-700">
                                    <p>Everest Base Camp via Gokyo- Chola Pass Trek is an absolute adventure into the heart of the Everest region, triumphing over one of the highest passes in the world, Chola Pass. This trek offers us the opportunity to be in the mesmerizing Gokyo Valley at the edge of serene Gokyo Lake.</p>
                                    <p>Further, the tourist can hike to the summit of Gokyo Ri, which is located at an altitude of 5,357 m (17,575 ft)-high and offers spectacular views of the entire Everest Himalayan range. The thrills of adventure while passing Chola Pass will be an adventure for a lifetime.</p>
                                    <p>Everest Base Camp via Gokyo-Chola Pass trekking is also a perfect choice for experiencing Sherpa Land and its culture. Apart from these, this trek takes you via Sagarmatha National Park, which is an abode of Alpine vegetation and wildlife. The glaciers, natural falls and landscapes, and high pasturelands are other highlights of this trekking.</p>
                                </div>
                            </div>
                            {/** Photos & Videos */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Photos & Videos</h2>
                                <div className="grid sm:grid-cols-3 gap-3 [&_img]:w-full [&_img]:h-full [&_img]:object-cover [&_img]:rounded-lg [&_img]:object-center w-full">
                                    <div className="sm:col-span-2 w-max-full">
                                        <img src="/assets/trek-1.png" />
                                    </div>
                                    <div className="sm:col-span-1 grid gap-3">
                                        <img src="/assets/trek-3.png"/>
                                        <img src="/assets/trek-4.png"/>
                                    </div>
                                </div>
                                <div className="mt-3 relative rounded-lg overflow-hidden [&_img]:w-full [&_img]:h-full [&_img]:object-cover [&_img]:rounded-lg [&_img]:object-center">
                                    <img src="/assets/trek-5.png" />
                                    <div className="absolute top-0 left-0 w-full h-full bg-black/25"></div>
                                    <div className="absolute top-1/2 left-1/2 -translate-1/2">
                                        <button className="inline-block p-6 text-white bg-white/50 rounded-full cursor-pointer">
                                            <FaPlay className="size-8"/>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            {/** Plan/Itinerary */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Plan/Itinerary</h2>
                                <div className="[&_p]:mb-4 text-md text-gray-700">
                                    <CustomAccordion data={planData}/>
                                </div>
                            </div>
                            {/** Route Map */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Route Map</h2>
                                <div className="text-md text-gray-700 mb-4 w-full">
                                    <img
                                        src="https://media.app.alpineramble.com/uploads/package/map/everest-base-camp-trekking-route-map.webp"
                                        className="w-full rounded-lg"
                                    />
                                </div>
                            </div>
                            {/** Cost Details */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Cost Details</h2>
                                <div className="[&_p]:mb-4 text-md text-gray-700">
                                    <h4 className="text-xl font-semibold">Includes</h4>
                                    <ul className="list-outside list-image-(--list-bullet-img-includes) [&>li]:pb-2 pl-7">
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                        <li>Complimentary use of any necessary trekking equipment, such as sleeping bags, down jackets, duffle bags, walkie-talkies, and an oximeter to monitor your oxygen and pulse.</li>
                                        <li>Fun souvenirs of your Himalayan adventure: a trekking route map, a t-shirt emblazoned with the Alpine Ramble Treks logo, and a hat! or cap.</li>
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                        <li>Complimentary use of any necessary trekking equipment, such as sleeping bags, down jackets, duffle bags, walkie-talkies, and an oximeter to monitor your oxygen and pulse.</li>
                                        <li>Fun souvenirs of your Himalayan adventure: a trekking route map, a t-shirt emblazoned with the Alpine Ramble Treks logo, and a hat! or cap.</li>
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                    </ul>

                                    <h4 className="text-xl font-semibold mt-4">Excludes</h4>
                                    <ul className="list-outside list-image-(--list-bullet-img-excludes) [&>li]:pb-2 pl-7">
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                        <li>Complimentary use of any necessary trekking equipment, such as sleeping bags, down jackets, duffle bags, walkie-talkies, and an oximeter to monitor your oxygen and pulse.</li>
                                        <li>Fun souvenirs of your Himalayan adventure: a trekking route map, a t-shirt emblazoned with the Alpine Ramble Treks logo, and a hat! or cap.</li>
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                        <li>Complimentary use of any necessary trekking equipment, such as sleeping bags, down jackets, duffle bags, walkie-talkies, and an oximeter to monitor your oxygen and pulse.</li>
                                        <li>Fun souvenirs of your Himalayan adventure: a trekking route map, a t-shirt emblazoned with the Alpine Ramble Treks logo, and a hat! or cap.</li>
                                        <li>Free guided Kathmandu Valley sightseeing tour (Swayambhu, Boudhanath, and Pashupatinath temple)</li>
                                        <li>Free transfers to and from Tribhuvan International Airport upon arrival and departure.</li>
                                    </ul>
                                </div>
                            </div>
                            {/** FAQ */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-3">Frequently Asked Question?</h2>
                                <div className="">
                                    <Accordion id="faq-details" items={faqs[0].items} />
                                </div>
                            </div>
                            {/** Client Reviews */}
                            <div className="mb-4">
                                <h2 className="text-2xl font-semibold mb-4">Client Reviews</h2>
                                <div className="flex flex-col gap-4 mb-6">
                                    <PackageReply/>
                                    <PackageReply/>
                                </div>
                                <div className="bg-gray-100 px-5 py-6 rounded-xl">
                                    <h3 className="text-xl font-semibold mb-4">Add Your Review</h3>
                                    <div className="flex gap-4 items-center mb-4">
                                        <span className="text-lg font-semibold text-gray-500">Your Rating</span>
                                        <ReviewRatingInput value={4} onInput={(val) => console.log('Rating changed: ' + val)} />
                                    </div>
                                    <div className="flex flex-col gap-5 w-full mb-6">
                                        <div className="grid sm:grid-cols-2 gap-5">
                                            <FloatingInput name="name" label="Your name"/>
                                            <FloatingInput name="phone" label="Your phone"/>
                                        </div>
                                        <FloatingInput type="email" name="email" label="Your email"/>
                                        <FloatingTextarea type="email" name="email" label="Type your message"/>
                                    </div>
                                    <div className="mb-4">
                                        <Button variant="primary" size="lg" className="!text-md font-medium">
                                            Post Your Comment
                                            <CgArrowLongRight />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="col-span-1">
                            <div className="rounded-xl shadow-lg border border-gray-100 p-5 mb-8">
                                <div className="flex gap-4 justify-between mb-4">
                                    <p className="text-primary font-medium">Price Form</p>
                                    <p className="text-primary text-lg font-semibold">$1200</p>
                                </div>
                                <div className="rounded-xl overflow-hidden border border-primary mb-4">
                                    <div className="bg-primary text-white text-md px-3 py-2 border border-primary">
                                        Group Discount Price
                                    </div>
                                    <div className="px-4 py-3 grid gap-1">
                                        {prices.map((price, index) => (
                                            <p className="flex justify-between gap-2" key={index}>
                                                <span>{price.title}</span>
                                                <span>${price.price}</span>
                                            </p>
                                        ))}
                                    </div>
                                </div>
                                <div className="mb-4">
                                    <Button variant="primary" block size="lg" className="rounded-xl font-medium mb-3">Book This Trip</Button>
                                    <Button variant="primary" block size="lg" outline className="rounded-xl font-medium">Customize a Trip</Button>
                                </div>
                                <div className="text-center">
                                    <button className="group/btn-download text-xs font-semibold text-gray-600 hover:text-gray-800 inline-flex items-center gap-1 cursor-pointer">
                                        <Icon name="file-download" size="16" className="fill-gray-600 group-hover/btn-download:fill-gray-800"/>
                                        Download a Brouchure
                                    </button>
                                </div>
                            </div>

                            <LiveSupportCard/>
                        </div>
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
