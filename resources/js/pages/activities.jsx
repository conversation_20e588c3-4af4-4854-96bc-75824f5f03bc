import ActivityCard from '../components/package/activity-card.jsx';
import Breadcrumb from '../components/shared/breadcrumb.jsx';
import HeroTopSection from '../components/shared/hero-top-section.jsx';
import { activities } from '../data/dummy';
import AppLayout from '../layouts/app-layout.jsx';

export default function Destinations({ title, destination }) {
    const breadcrumbLinks = [
        { title: 'Destinations', href: '/destinations' },
        { title }
    ];

    return (
        <>
            <AppLayout title={title}>
                <HeroTopSection title={title}>
                    <Breadcrumb links={breadcrumbLinks} />
                </HeroTopSection>
                <section className="container px-4 py-12">
                    <div className="text-md mx-auto mt-8 text-black md:w-[80%]">
                        <p className="mb-8">
                            Nepal was NepalIt was exactly 6 am in the morning
                            when my travel partner <PERSON><PERSON><PERSON> and I took the bus
                            to Bhaktapur from Chabahil for a day in Bhaktapur
                            Durbar Square. Curious to know why we head out that
                            early? Well, you need to keep reading the blog till
                            the last, and I’ll try to keep it shorter and more
                            enjoyable.
                        </p>
                        <p className="mb-0">
                            Nepal was exactly 6 am in the morning Nepal when my
                            travel partner Upashana and I took the bus to
                            Bhaktapur from Chabahil for a day in Bhaktapur
                            Durbar Square. Curious to know why we head out that
                            early? Well, you need to keep reading the blog till
                            the last, and I’ll try to keep it shorter and more
                            enjoyable Nepal.
                        </p>
                    </div>
                </section>
                <section className="container px-4 pt-8 pb-18">
                    <div className="py-4 text-center">
                        <h2 className="ff-myrd-web mb-3 text-4xl text-primary md:text-5xl">
                            Activities
                        </h2>
                    </div>
                    <div className="grid gap-6 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                        {[...activities, ...activities].map(
                            (activity, index) => (
                                <ActivityCard
                                    key={activity.slug + index}
                                    name={activity.name}
                                    image={activity.image}
                                    packages={activity.packages}
                                    href={`/activities/${destination}/${activity.slug}`}
                                />
                            )
                        )}
                    </div>
                </section>
            </AppLayout>
        </>
    );
}
