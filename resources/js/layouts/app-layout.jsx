import { Head } from '@inertiajs/react';
import Footer from '../components/footer.jsx';
import Header from '../components/header.jsx';
import Topbar from '../components/topbar.jsx';
import { twMerge } from 'tailwind-merge';

export default function AppLayout({ children, title, meta = null, containerClass }) {
    return (
        <>
            <Head
                title={title}
                meta={meta ?? { description: 'Everest Travel' }}
            />
            <div className="absolute top-0 z-10 w-full">
                <Topbar />
                <Header />
            </div>
            <main className={twMerge(containerClass)}>{children}</main>
            <Footer />
        </>
    );
}
