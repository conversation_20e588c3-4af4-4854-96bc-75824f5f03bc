@import url('https://fonts.googleapis.com/css2?family=Montez&display=swap');
@import url('https://fonts.cdnfonts.com/css/myriad-web-pro');
@import url('https://fonts.cdnfonts.com/css/myriad-pro');
@import url('https://fonts.bunny.net/css?family=open-sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i');
@import url('https://fonts.bunny.net/css?family=roboto:300,300i,400,400i,500,500i,700,700i,900,900i');

@import 'tailwindcss';

/* Preline UI */
@import '../../node_modules/preline/variants.css';
@import "../../node_modules/preline/src/plugins/datepicker/styles.css";

@source "../../node_modules/preline/dist/*.js";

@source "../**/*.blade.php";
@source "../**/*.js";
@source "../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php";
@source "../../storage/framework/views/*.php";

@custom-variant dark (&:where(.dark, .dark *));

@plugin "@tailwindcss/forms";
@plugin "daisyui";

@plugin "daisyui/theme" {
    name: everest;
    default: true;
    prefersdark: true;
    color-scheme: light;
    --color-base-100: oklch(100% 0 0);
    --color-base-200: oklch(98% 0 0);
    --color-base-300: oklch(95% 0 0);
    --color-base-content: oklch(21% 0.006 285.885);
    --color-primary: oklch(39% 0.09 158.03);
    --color-primary-content: oklch(98% 0.018 155.826);
    --color-secondary: oklch(55% 0 0);
    --color-secondary-content: oklch(92% 0 0);
    --color-accent: oklch(77% 0.152 181.912);
    --color-accent-content: oklch(38% 0.063 188.416);
    --color-neutral: oklch(14% 0.005 285.823);
    --color-neutral-content: oklch(92% 0.004 286.32);
    --color-info: oklch(62% 0.214 259.815);
    --color-info-content: oklch(37% 0.146 265.522);
    --color-success: oklch(72% 0.219 149.579);
    --color-success-content: oklch(37% 0.077 168.94);
    --color-warning: oklch(82% 0.189 84.429);
    --color-warning-content: oklch(41% 0.112 45.904);
    --color-error: oklch(63% 0.237 25.331);
    --color-error-content: oklch(39% 0.141 25.723);
    --radius-selector: 0.5rem;
    --radius-field: 1rem;
    --radius-box: 1rem;
    --size-selector: 0.25rem;
    --size-field: 0.21875rem;
    --border: 1.5px;
    --depth: 1;
    --noise: 0;
}

@theme {
    --font-montez: 'Montez', cursive;
    --font-myriad-web: 'Myriad Web Pro', sans-serif;
    --font-myriad: 'Myriad Pro', sans-serif;
    --font-open-sans: 'Open Sans', sans-serif;
    --font-roboto: 'Roboto', sans-serif;
    --font-sans: var(--font-open-sans);

    --color-th-green-800: #004728;
    --color-th-green-600: #0e653f;
    --color-th-green-550: #128053;
    --color-th-green-500: #0eeb8a;
    --color-th-green-100: #e3fef2;
    --color-th-greenish-500: #a9c1b2;
    --color-th-greenish-100: #ebf3ee;

    --text-2\.5xl: 1.5rem;

    --radius-5xl: 2.5rem;
    --radius-6xl: 3rem;

    --breakpoint-sm: 30rem;

    --tracking-tightest: -0.125rem;
}

:root {
    --hero-section-bg: '';
    --hero-abstract-bg: '';
    --breadcrumb-separator: '';
    --list-bullet-img-includes: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 27.5' fill='none' stroke='%23555555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' style='padding-top:4px'%3E%3Cpath d='M21 10.656V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h12.344'/%3E%3Cpath d='m9 11 3 3L22 4'/%3E%3C/svg%3E");
    --list-bullet-img-excludes: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='20' height='20' viewBox='0 0 24 27.5' fill='none' stroke='%23555555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round' style='padding-top:4px'%3E%3Crect width='18' height='18' x='3' y='3' rx='2' ry='2'/%3E%3Cpath d='m15 9-6 6'/%3E%3Cpath d='m9 9 6 6'/%3E%3C/svg%3E");
}

.ff-montez {
    @apply font-montez;
}

.ff-myrd-web {
    @apply font-myriad-web;
}

.ff-myrd-pro {
    @apply font-myriad;
}

.container {
    margin-left: auto !important;
    margin-right: auto !important;
    max-width: 1140px;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.hero-text-shadow {
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(0, 0, 0, 0.5),
    0 0 30px rgba(0, 0, 0, 0.5);
}

.hero-lead {
    line-height: 1.017;
    text-shadow: 0 6px 6px rgba(0, 0, 0, 0.35),
    -4px -4px 6px rgba(0, 0, 0, 0.35),
    0 -4px 6px rgba(0, 0, 0, 0.35);
}

.hero-pretitle {
    text-shadow: 0px 3px 5.4px rgba(0, 0, 0, 0.54);
}

.hero-text {
    line-height: 1.455;
    text-shadow: 4px 4px 9px rgba(0, 0, 0, 0.89),
    0 0 10px rgba(0, 0, 0, 0.99),
    0 0 15px rgba(0, 0, 0, 0.99),
    0 0 20px rgba(0, 0, 0, 0.99);
}

.slide-card-shadow {
    box-shadow: 4px 4px 15px rgba(0, 0, 0, 0.55),
    0 0 10px rgba(0, 0, 0, 0.55);
}

.hero-slider .splide__slide {
    @apply flex cursor-grab items-center justify-center;
}

.hero-slider .slide-card-container {
    @apply h-[340px] w-[205px];
}

.hero-slider .is-prev + .is-active .slide-card-container {
    @apply h-[385px] w-[225px];
}

.shadow-edge {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.95),
    0 0 10px rgba(0, 0, 0, 0.65),
    0 0 15px rgba(0, 0, 0, 0.55),
    0 0 20px rgba(0, 0, 0, 0.45);
}

.photo-card-shadow {
    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.25);
}

.landing-hero-section {
    background-image: var(--bg-img);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    position: relative;
}

.landing-hero-section::before {
    background-image: var(--bg-abstract);
    background-repeat: repeat-x;
    background-size: contain;
    background-position: left calc(100% + 1px);
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 120px;
    z-index: 0;
}

.hero-section {
    --hero-abstract-bg: url("/assets/abstract-watercolor-hero-background.png");
    --hero-section-bg: url("/assets/hero-top-bg.jpg");
    background-image: var(--hero-section-bg);
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    position: relative;

}

.hero-section::before {
    content: '';
    background-image: var(--hero-abstract-bg);
    position: absolute;
    bottom: 0;
    left: 0;
    background-repeat: repeat-x;
    z-index: 1;
    height: 100%;
    width: 100%;
    background-position: left calc(100% + 20px);
    background-size: 1100px 80px;
}

.breadcrumbs li:not(:first-child)::before {
    --breadcrumb-separator: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDI0IDI0IiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmZmZmYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBjbGFzcz0ibHVjaWRlIGx1Y2lkZS1jaGV2cm9ucy1yaWdodC1pY29uIGx1Y2lkZS1jaGV2cm9ucy1yaWdodCI+PHBhdGggZD0ibTYgMTcgNS01LTUtNSIvPjxwYXRoIGQ9Im0xMyAxNyA1LTUtNS01Ii8+PC9zdmc+');
    content: var(--breadcrumb-separator) !important;
    margin: 0 !important;
    margin-top: 0.25rem !important;
    display: inline-flex;
    padding: 0 0.5rem !important;
    border: none;
    rotate: 0deg !important;
    height: 12px !important;
    width: unset !important;
    align-items: center;
    opacity: 80% !important;
}

.blog-content .blog-quote-icon {
    color: #128053 !important;
}

.blog-content blockquote {
    --quote-color: #128053;
    --quote-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='64' height='64' viewBox='0 0 24 24' fill='none' stroke='%23128053' style='color: var(--quote-color)' class='blog-quote-icon' stroke-width='0.75' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z'/%3E%3Cpath d='M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z'/%3E%3C/svg%3E");
}

.blog-content blockquote::before {
    color: var(--quote-color);
    content: var(--quote-icon);
    display: block;
}

.blog-content blockquote > span {
    display: block;
    margin-top: 1rem;
}

.blog-content blockquote > span::before {
    content: "----------";
    display: inline-block;
    letter-spacing: -0.115rem;
    margin-right: 0.5rem;
}
